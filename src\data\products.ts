import { Product } from "@/types";

// Optimized product data with better image URLs and lazy loading
export const allProducts: Product[] = [
  {
    id: 1,
    name: "FanIce Vanilla 120ml",
    description: "Classic vanilla ice cream made with pure vanilla extract, delivering a smooth, creamy texture and timeless flavor perfect for any occasion.",
    price: 350,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/120ml_vanilla?wid=400&fmt=webp&fit=wrap",
    category: "120ml",
    featured: false,
  },
  {
    id: 2,
    name: "FanIce Strawberry 120ml",
    description: "Sweet and refreshing strawberry ice cream bursting with natural fruit flavor, creating a perfect balance of creaminess and berry freshness.",
    price: 350,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/120ml_stawberry?wid=400&fmt=webp&fit=wrap",
    category: "120ml",
    featured: false,
  },
  {
    id: 3,
    name: "Fan<PERSON>ce Banana 120ml",
    description: "Rich and creamy banana ice cream made with real banana puree, offering a tropical twist to your dessert experience.",
    price: 350,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/120ml_banana?wid=400&fmt=webp&fit=wrap",
    category: "120ml",
    featured: false,
  },
  {
    id: 4,
    name: "FanIce Chocolate 120ml",
    description: "Indulgent chocolate ice cream crafted with premium cocoa, delivering an intense chocolate experience in every spoonful.",
    price: 350,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/120ml_chocolate?wid=400&fmt=webp&fit=wrap",
    category: "120ml",
    featured: false,
  },
  {
    id: 5,
    name: "FanIce Vanilla 250ml",
    description: "Our signature vanilla ice cream in a larger size, perfect for sharing. Made with premium ingredients for a rich, creamy texture.",
    price: 650,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/250_Vanilla?wid=400&fmt=webp&fit=wrap",
    category: "250ml",
    featured: false,
  },
  {
    id: 6,
    name: "FanIce Strawberry 250ml",
    description: "Family-sized strawberry ice cream packed with natural strawberry flavor, perfect for sharing or keeping your freezer stocked with summer freshness.",
    price: 650,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/250_Strawberry?wid=400&fmt=webp&fit=wrap",
    category: "250ml",
    featured: false,
  },
  {
    id: 7,
    name: "FanIce Banana 250ml",
    description: "Creamy banana ice cream in a generous portion, made with real banana puree for an authentic tropical taste experience.",
    price: 650,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/250_Banana?wid=400&fmt=webp&fit=wrap",
    category: "250ml",
    featured: false,
  },
  {
    id: 8,
    name: "FanIce Chocolate 250ml",
    description: "Rich chocolate ice cream in a sharing size, perfect for chocolate lovers. Made with premium cocoa for an intense chocolate experience.",
    price: 650,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/250_Chocolate?wid=400&fmt=webp&fit=wrap",
    category: "250ml",
    featured: false,
  },
  {
    id: 9,
    name: "FanIce Vanilla 450ml",
    description: "Our premium vanilla ice cream in a family size, perfect for gatherings. Made with pure vanilla extract for a rich, authentic flavor.",
    price: 1200,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/450ml+Vanilla?wid=400&fmt=webp&fit=wrap",
    category: "450ml",
    featured: false,
  },
  {
    id: 10,
    name: "FanIce Strawberry 450ml",
    description: "Family-sized strawberry ice cream perfect for parties and gatherings. Bursting with natural strawberry flavor in every scoop.",
    price: 1200,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/450ml+Strawberry?wid=400&fmt=webp&fit=wrap",
    category: "450ml",
    featured: false,
  },
  {
    id: 11,
    name: "FanIce Vanilla 900ml",
    description: "Our largest size of premium vanilla ice cream, perfect for events and large gatherings. Rich, creamy, and authentically flavored.",
    price: 2200,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/900ml+Vanilla?wid=400&fmt=webp&fit=wrap",
    category: "900ml",
    featured: false,
  },
  {
    id: 12,
    name: "FanIce Strawberry 900ml",
    description: "Party-sized strawberry ice cream perfect for special occasions. Made with real strawberries for a fresh, natural taste.",
    price: 2200,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/900ml+Strawberry?wid=400&fmt=webp&fit=wrap",
    category: "900ml",
    featured: false,
  },
  {
    id: 13,
    name: "Goslo Cookies & Cream 320ml",
    description: "Premium ice cream blended with real cookie pieces, creating the perfect balance of smooth cream and crunchy cookie texture.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Cookies_cream?wid=400&fmt=webp&fit=wrap",
    category: "320ml",
    featured: true,
  },
  {
    id: 14,
    name: "Goslo Peanut & Butter 320ml",
    description: "Rich peanut butter ice cream swirled with butter cream, offering a delightful nutty experience with every spoonful.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Peanut_butter?wid=400&fmt=webp&fit=wrap",
    category: "320ml",
    featured: true,
  },
  {
    id: 15,
    name: "Goslo Salted Caramel 320ml",
    description: "Smooth vanilla ice cream with ribbons of salted caramel throughout, creating a perfect sweet and salty flavor combination.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Salted_caramel?wid=400&fmt=webp&fit=wrap",
    category: "320ml",
    featured: true,
  },
  {
    id: 16,
    name: "Goslo Chocolate Almond 320ml",
    description: "Premium chocolate ice cream loaded with roasted almond pieces, delivering a perfect blend of rich chocolate and nutty crunch.",
    price: 850,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/sm_Chocolate_almond?wid=400&fmt=webp&fit=wrap",
    category: "320ml",
    featured: true,
  },
  {
    id: 17,
    name: "Goslo Cookies & Cream 460ml",
    description: "Family-sized cookies and cream ice cream packed with premium cookie pieces, perfect for sharing this classic favorite.",
    price: 1500,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/Cookies_cream?wid=400&fmt=webp&fit=wrap",
    category: "460ml",
    featured: true,
  },
  {
    id: 18,
    name: "Goslo Peanut & Butter 460ml",
    description: "Generous portion of our signature peanut butter ice cream, perfect for peanut butter lovers seeking a rich, creamy treat.",
    price: 1500,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/Peanut_butter?wid=400&fmt=webp&fit=wrap",
    category: "460ml",
    featured: true,
  },
  {
    id: 19,
    name: "Goslo Salted Caramel 460ml",
    description: "Large format of our popular salted caramel ice cream, featuring perfect swirls of salted caramel in smooth vanilla ice cream.",
    price: 1500,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/Salted_caramel?wid=400&fmt=webp&fit=wrap",
    category: "460ml",
    featured: true,
  },
  {
    id: 20,
    name: "Goslo Chocolate Almond 460ml",
    description: "Family-sized chocolate almond ice cream, combining rich chocolate ice cream with premium roasted almonds for a luxurious dessert experience.",
    price: 1500,
    image: "https://smartmedia.digital4danone.com//is/image/danonecs/Chocolate_almond?wid=400&fmt=webp&fit=wrap",
    category: "460ml",
    featured: true,
  },
];

// Get unique categories from the products
export const categories = ["All", ...new Set(allProducts.map(product => product.category))];

// Helper functions for better performance
export const getFeaturedProducts = () => allProducts.filter(product => product.featured);
export const getProductsByCategory = (category: string) => {
  if (category === "All") return allProducts;
  if (category === "Goslo Premium") {
    return allProducts.filter(product => product.category === "320ml" || product.category === "460ml");
  }
  return allProducts.filter(product => product.category === category);
};

export const searchProducts = (products: Product[], searchTerm: string) => {
  if (!searchTerm) return products;
  const term = searchTerm.toLowerCase();
  return products.filter(product =>
    product.name.toLowerCase().includes(term) ||
    product.description.toLowerCase().includes(term) ||
    product.category.toLowerCase().includes(term)
  );
};
