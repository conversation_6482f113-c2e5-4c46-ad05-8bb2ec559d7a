
import { memo } from "react";
import { Link } from "react-router-dom";
import { Product } from "@/types";

interface ProductCardProps {
  product: Product;
  index: number;
  priority?: boolean;
}

const ProductCard = memo(({ product, priority = false }: ProductCardProps) => {
  // Check if this is a featured product (for homepage display)
  const isFeaturedDisplay = product.id <= 6 && product.price === 0;

  const imageProps = {
    src: product.image,
    alt: product.name,
    className: "absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105",
    loading: (priority ? "eager" : "lazy") as "eager" | "lazy",
    decoding: "async" as const,
    sizes: "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw",
    ...(priority && { fetchPriority: "high" as const }),
  };

  const content = (
    <>
      <div className="relative pb-[100%] overflow-hidden bg-mint/10">
        <img {...imageProps} />
        {product.featured && (
          <span className="absolute top-4 left-4 px-3 py-1 bg-primary/90 backdrop-blur-sm text-white text-xs uppercase tracking-wider rounded-full">
            Premium
          </span>
        )}
      </div>

      <div className="p-6">
        <h3 className="font-serif text-xl font-semibold text-softBlack group-hover:text-primary transition-colors duration-300">
          {product.name}
        </h3>
        <p className="mt-2 text-sm text-muted-foreground line-clamp-2">
          {product.description}
        </p>
        {!isFeaturedDisplay && (
          <div className="mt-4">
            <span className="text-lg font-semibold text-primary">
              ₦{product.price.toLocaleString()}
            </span>
          </div>
        )}
      </div>
    </>
  );

  return (
    <div className="group relative bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300">
      {isFeaturedDisplay ? (
        <div className="block">{content}</div>
      ) : (
        <Link to={`/products/${product.id}`} className="block">
          {content}
        </Link>
      )}
    </div>
  );
});

export default ProductCard;
